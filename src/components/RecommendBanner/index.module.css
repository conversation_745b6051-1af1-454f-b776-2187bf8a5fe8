.bannerContainer {
  border-radius: 56px;
  background: linear-gradient(90deg, rgba(210, 76, 255, 0.8) 0%, rgba(250, 62, 62, 0.8) 50%, rgba(245, 82, 0, 0.8) 100%);
  box-shadow: 0 8px 16px 0 var(--tbpc-home-more-box-shadow-color, rgba(173, 14, 54, 0.16));
  width: 288px;
  height: 56px;
  cursor: pointer;
  position: relative;
}

.bannerContent {
  border-radius: inherit;
  background: var(--bg-color, #fff);
  position: absolute;
  inset: 1px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  box-sizing: border-box;
  padding: 6px 12px;
}
  
.carouselImages {
  width: 64px;
  height: 32px;
  border-radius: 16px;
  position: relative;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}
.carouselImage {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  border: 1px solid #fff;
  background-color: #fff;
  box-sizing: border-box;
  position: absolute;
  left: 0;
  animation: slide 12s infinite;
}


.image-0 {
  animation-delay: -10s;
}

.image-1 {
  animation-delay: -8s;
}

.image-2 {
  animation-delay: -6s;
}

.image-3 {
  animation-delay: -4s;
}

.image-4 {
  animation-delay: -2s;
}

@keyframes slide {
  0%,
  90%,
  100% {
    opacity: 1;
    z-index: 4;
    transform: translate(0, 0%);
  }

  6.67%,
  16.67% {
    z-index: 5;
    transform: translate(16px, 0%);
  }

  23.33%,
  33.33% {
    z-index: 6;
    transform: translate(32px, 0%);
  }

  40%,
  50% {
    z-index: 7;
    transform: translate(64px, 0%);
  }

  56.67%,
  66.67% {
    opacity: 1;
    z-index: 1;
    transform: translate(64px, 0%);
  }

  70% {
    opacity: 0;
    transform: translate(64px, 0%);
  }

  72% {
    opacity: 0;
    z-index: 0;
    transform: translate(0%, 0%);
  }

  73.33%,
  83.33% {
    opacity: 1;
    z-index: 0;
    transform: translate(0%, 0%);
  }
}
  
.textContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  margin-left: 8px;
}
  
.bannerText {
  width: 38px;
  height: 24px;
  background-image: var(--tbpc-shopping-url, url(https://img.alicdn.com/imgextra/i3/O1CN01l5sZUi1KExo8ppQ5x_!!6000000001133-2-tps-152-96.png));
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.bannerTextMore {
  margin-left: 4px;
  font-size: 14px;
  line-height: 1;
  color: var(--tb-brand-light-color, #ff5000);
  white-space: nowrap;
}
  
.arrowIcon {
  margin-left: 12px;
  width: 32px;
  height: 32px;
  background-image: var(--tbpc-more-arrow-url, url(https://img.alicdn.com/imgextra/i1/O1CN01eTU5U71Cq2wxFjRgI_!!6000000000131-2-tps-64-64.png));
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
