import React, { useCallback, useState, useEffect, useRef } from 'react';
import { throttle } from 'lodash-es';
import styles from './index.module.css';
import { goldlogRecord } from '@/utils/goldlog';
import { getPageSpm } from '@/utils/spm';

interface RecommendBannerProps {
  id?: string;
}

const IFRAME_STYLES = `
  position: fixed;
  top: 0;
  left: 0;
  border: none;
  display: block;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  background-color: #fff;
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
`;

const CLOSE_BTN_STYLES = `
  position: fixed;
  bottom: 20px;
  flex-direction: row;
  justify-content: center;
  left: 50%;
  display: none;
  align-items: center;
  width: 128px;
  margin-left: -64px;
  height: 48px;
  border: 1px solid rgba(0, 0, 0, 0.02);
  background-color: #F1F3F5;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  font-size: 12px;
  z-index: 10001;
  cursor: pointer;
`;

const itemList: any[] = [
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i4/O1CN01WG2Q681mzVHpJHS7G_!!6000000005025-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01FMI1YF1ROlaqRsNFr_!!6000000002102-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01PWq7dv1Bzpn1yBfjv_!!6000000000017-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i4/O1CN01lStfjd23ZiSbI6W4D_!!6000000007270-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN011WHLDE1jlMD4XynJG_!!6000000004588-2-tps-1080-1440.png_q95.jpg',
  },
  {
    pictUrl:
      'https://img.alicdn.com/imgextra/i2/O1CN01cimNyL28nWTXp7VgU_!!6000000007977-2-tps-1080-1440.png_q95.jpg',
  },
];

const spmC = 'banner';
const spmD = 'd1';

const guangUrl =
  location.host.indexOf('pre-') >= 0
    ? 'https://pre-wormhole.tmall.com/wow/z/tbhome/guang/c6yj86r6f7m4k2dd3jxn?remove_framework=true'
    : 'https://guang.taobao.com/?remove_framework=true';
const RecommendBanner: React.FC<RecommendBannerProps> = ({ id }) => {
  const reachedBottomRef = useRef(false);
  const [reachedBottom, setReachedBottom] = useState(reachedBottomRef.current);
  const startYRef = useRef(0);
  const isDraggingRef = useRef(false);
  const wheelCountRef = useRef(0);
  const wheelTimerRef = useRef<any>(null);

  const isPageBottom = () => {
    const { scrollHeight } = document.documentElement;
    const scrollTop = window.scrollY || document.documentElement.scrollTop;
    const clientHeight =
      window.innerHeight || document.documentElement.clientHeight;
    return scrollHeight - scrollTop - clientHeight < 10;
  };

  const resetState = useCallback(() => {
    reachedBottomRef.current = false;
    setReachedBottom(false);
  }, []);

  useEffect(() => {
    // 曝光
    goldlogRecord({
      logKey: '/tbpcclient.newpc.recommend',
      gmKey: 'EXP',
      goKey: {
        spm: getPageSpm(spmC, spmD),
      },
    });
    const handleMouseDown = (e: MouseEvent) => {
      isDraggingRef.current = true;
      startYRef.current = e.clientY;
    };

    const handleMouseMoveBase = (e: MouseEvent) => {
      if (!isDraggingRef.current) return;

      const deltaY = e.clientY - startYRef.current;
      // console.log(
      //   'e.clientY, startYRef.current=',
      //   e.clientY,
      //   startYRef.current,
      // );
      // console.log('mouse move deltaY:', deltaY);

      if (deltaY < -120 && isPageBottom() && !reachedBottomRef.current) {
        reachedBottomRef.current = true;
        setReachedBottom(true);
      }
    };

    const handleMouseMove = throttle(handleMouseMoveBase, 200);

    const handleMouseUp = () => {
      isDraggingRef.current = false;
    };

    const handleWheelBase = (e: WheelEvent) => {
      // console.log(
      //   'wheel deltaY:',
      //   e.deltaY,
      //   isPageBottom(),
      //   wheelCountRef.current,
      // );
      if (isPageBottom() && !reachedBottomRef.current) {
        if (wheelTimerRef.current) {
          clearTimeout(wheelTimerRef.current);
        }

        // 单次滚动距离大于120直接触发
        if (e.deltaY > 120) {
          reachedBottomRef.current = true;
          setReachedBottom(true);
          wheelCountRef.current = 0;
          return;
        }

        // 记录滚动次数
        wheelCountRef.current += 1;

        wheelTimerRef.current = setTimeout(() => {
          wheelCountRef.current = 0;
        }, 1000);

        // 滚动次数大于2且滚动距离大于30才触发
        if (wheelCountRef.current >= 2 && e.deltaY > 30) {
          reachedBottomRef.current = true;
          setReachedBottom(true);
          wheelCountRef.current = 0;
        }
      }
    };

    const handleWheel = throttle(handleWheelBase, 200);

    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('wheel', handleWheel);
      if (wheelTimerRef.current) {
        clearTimeout(wheelTimerRef.current);
      }
    };
  }, []);

  const openGuangPage = useCallback(() => {
    // 触发
    goldlogRecord({
      logKey: '/tbpcclient.newpc.recommend',
      gmKey: 'CLK',
      goKey: {
        spm: getPageSpm(spmC, spmD),
      },
    });
    let guang = document.getElementById('guang_frame') as HTMLIFrameElement;
    if (guang) {
      guang.style.display = 'block'; // 重新显示已存在的 iframe
      guang.style.transform = 'translateY(100%)'; // 重置位置
    } else {
      guang = document.createElement('iframe');
      guang.id = 'guang_frame';
      guang.src = guangUrl;
      guang.style.cssText = IFRAME_STYLES;
      document.body.appendChild(guang);
    }

    const closeBtn = document.createElement('div');
    closeBtn.id = 'frame_close_btn';
    closeBtn.style.cssText = CLOSE_BTN_STYLES;
    closeBtn.innerHTML = `
      <div style="font-family:PingFang SC; font-size:16px; font-weight:600; letter-spacing:normal; color: rgba(0, 0, 0, 0.92); margin-right:8px;">回到首页</div>
      <img style="width: 24px;height: 24px;" src="https://gw.alicdn.com/imgextra/i2/O1CN010RgLC02431nt0jqmC_!!6000000007334-2-tps-96-96.png" />
    `;

    // 使用 requestAnimationFrame 确保样式更新
    requestAnimationFrame(() => {
      guang.style.transform = 'translateY(0)';
      setTimeout(() => {
        closeBtn.style.display = 'flex';
        // 曝光
        goldlogRecord({
          logKey: '/tbpcclient.newpc.recommend_close',
          gmKey: 'EXP',
          goKey: {
            spm: getPageSpm(spmC, spmD),
          },
        });
      }, 1000);
    });

    closeBtn.onclick = () => {
      closeBtn.style.display = 'none';
      guang.style.transform = 'translateY(100%)';
      // 曝光
      goldlogRecord({
        logKey: '/tbpcclient.newpc.recommend_close',
        gmKey: 'CLK',
        goKey: {
          spm: getPageSpm(spmC, spmD),
        },
      });
      // 监听过渡结束后重置状态
      guang.addEventListener(
        'transitionend',
        () => {
          resetState();
          guang.style.display = 'none';
        },
        { once: true },
      );
    };

    document.body.appendChild(closeBtn);
  }, [resetState]);

  // 监听reachedBottom变化
  useEffect(() => {
    if (reachedBottom) {
      openGuangPage();
    }
  }, [reachedBottom, openGuangPage]);

  const clickHandler = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();
      openGuangPage();
    },
    [openGuangPage],
  );

  return (
    <div className={styles.bannerContainer} id={id} onClick={clickHandler}>
      <div className={styles.bannerContent}>
        <div className={styles.carouselImages}>
          {itemList.map((item: any, index: number) => (
            <img
              key={index}
              className={`${styles.carouselImage} ${styles[`image-${index}`]}`}
              src={item?.pictUrl}
            />
          ))}
        </div>
        <div className={styles.textContainer}>
          <div className={styles.bannerText} />
          <span className={styles.bannerTextMore}>发现更多好物</span>
          <div className={`${styles.arrowIcon}`} />
        </div>
      </div>
    </div>
  );
};

export default RecommendBanner;
