import type {
  IAvatarConfig,
  IBenefitBottomSlider,
  IBenefitCount,
  IBenefitRemoteSlider,
  ILoginAreaConfig,
  // INoLoginAd,
  // IOrderCountRes,
  IUserInfo,
} from '../../types';
import { LOGIN_STATUS } from '../../types';
import UserRights from '../UserRights';
import { goldLogUserClk } from '../../utils';
import { HomeUrlHref } from '@/utils';

// const NickUrlHref = `//login.taobao.com/member/login.jhtml?f=top&redirectURL=http%3A%2F%2F${window.location.host}%2F`;
const DefaultAvatarImg =
  '//gtms03.alicdn.com/tps/i3/TB1yeWeIFXXXXX5XFXXuAZJYXXX-210-210.png_80x80.jpg';
// const openShopHref =
//   window.g_config?.bizType === 'tmall'
//     ? '//zhaoshang.tmall.com/'
//     : '//ishop.taobao.com/openshop/tb_open_shop_landing.htm?accessCode=tbopenshop_personal';
// const followShopHref = '//i.taobao.com/my_itaobao/subscription';
// const addressHref = '//member1.taobao.com/member/fresh/deliver_address.htm';

function UserLogout(props: IUserLogoutProps) {
  const {
    userInfo,
    login,
    // orderCount,
    showOrder,
    avatarConfig,
    loginAreaConfig,
    // noLoginAd,
    benefitSliderList,
    benefitCount,
  } = props;
  const showAvatarConfig = !!(avatarConfig?.bgColor && avatarConfig?.iconUrl);
  const noStrongLogin =
    login === LOGIN_STATUS.NONE || login === LOGIN_STATUS.WEAK;
  // 弱登录+强登录： 用来控制 头像及昵称的跳转 文字hover后的样式
  const reachWeakLogin =
    login === LOGIN_STATUS.WEAK || login === LOGIN_STATUS.STRONG;
  const showHorizon = showOrder || noStrongLogin;

  return (
    <a
      className={`${showAvatarConfig ? 'member-wrapper-with-config' : 'member-wrapper'}`}
      href={HomeUrlHref}
      target="_blank"
      data-spm="d_bg"
    >
      <div
        className={`J_UserMemberWrap member-bd ${reachWeakLogin ? 'member-bd-login' : ''}`}
      >
        <div className="member-bd-left">
          <div
            className={`avatar-wrapper ${showAvatarConfig ? 'avatar-wrapper-with-config' : ''}`}
            // style={
            //   showAvatarConfig
            //     ? { backgroundColor: `${avatarConfig.bgColor}` }
            //     : {}
            // }
          >
            <div
              className="J_UserMemberHome member-home"
              onClick={() => {
                goldLogUserClk('profile', 'nick');
              }}
            >
              <div
                className={`J_UserMemberAvatar member-avatar ${showHorizon ? 'member-avatar-order' : ''}`}
                style={
                  showAvatarConfig
                    ? {
                        boxSizing: 'border-box',
                        border: `2px solid ${loginAreaConfig?.topColor || '#fff'}`,
                        transform: 'scale(0.96)',
                        backgroundImage: `url(${userInfo?.avatar || DefaultAvatarImg})`,
                      }
                    : {
                        backgroundImage: `url(${userInfo?.avatar || DefaultAvatarImg})`,
                      }
                }
              />
            </div>
          </div>
          <div
            className="J_UserMemberNickUrl member-nickurl"
            onClick={() => {
              if (login === LOGIN_STATUS.NONE) {
                return;
              }
              goldLogUserClk('profile', 'nick');
            }}
          >
            <span className="member-nick-info">
              {`${userInfo?.nick || ''}`}
            </span>
          </div>
          {/* 88vip标签 */}
          {showAvatarConfig && (
            // <img
            //   className={`member-avatar-tag ${showHorizon ? 'member-avatar-tag-order' : ''}`}
            //   src={avatarConfig?.iconUrl}
            // />
            <div className="member-avatar-tag-bg" />
          )}
        </div>
        {/* 88vip背景 */}
        {showAvatarConfig ? (
          <div className="member-bd-right">
            <span className="benefit-text">
              已享 <span>88VIP</span> 专属权益
            </span>
            <span className="tb-ifont arrow-icon">&#xe642;</span>
            <div className="benefit-background" />
          </div>
        ) : null}
      </div>
      <div className="member-ft">
        <UserRights
          benefitSliderList={benefitSliderList}
          benefitCount={benefitCount}
          showAvatarConfig={showAvatarConfig}
        />
      </div>
    </a>
  );
}

export default UserLogout;

interface IUserLogoutProps {
  /** 订单数量 */
  // orderCount: IOrderCountRes;
  /** 强登录/弱登录/未登录 */
  login: LOGIN_STATUS;
  /** 头像框/tag */
  avatarConfig: undefined | IAvatarConfig;
  /** 头像/昵称/问候词 */
  userInfo?: IUserInfo;
  /** 横纵展示控制 */
  showOrder?: boolean;
  loginAreaConfig?: ILoginAreaConfig;
  /** 未登录营销大卡 */
  // noLoginAd: INoLoginAd;
  /** 权益数量 */
  benefitCount: IBenefitCount;
  /** 权益轮播 */
  benefitSliderList: (IBenefitBottomSlider | IBenefitRemoteSlider)[];
}
