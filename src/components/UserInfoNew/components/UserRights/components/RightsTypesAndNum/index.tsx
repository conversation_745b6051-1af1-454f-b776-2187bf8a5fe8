import React, { useEffect, useMemo } from 'react';
import { isTrue } from '@/utils';
// import { showBenefitDrawer } from '../../../../..//BenefitDrawer';
import type { IBenefitBaseCount, IUserRightsProps, RewardData, RewardStatus } from '../../../../types';
import { goldLogUserClk, goldLogUserExp } from '../../../../utils';
import './index.less';

const BENEFIT_CONFIG_MAP: {
  [key: string]: {
    desc: string;
    logkey: string;
    url: string;
  };
} = {
  redEnvelop: {
    desc: '红包',
    logkey: 'dredpacket',
    url: 'https://i.taobao.com/my_itaobao/coupon?defaultTab=redEnvelope',
  },
  coupon: {
    desc: '优惠券',
    logkey: 'dcoupon',
    url: 'https://i.taobao.com/my_itaobao/coupon',
  },
  taoCoin: {
    desc: '淘金币抵',
    logkey: 'dtaojinbi',
    url: 'https://huodong.taobao.com/wow/z/tbhome/pc-growth/tao-coin',
  },
};

function convertToObjectArray(data: IBenefitBaseCount) {
  const result: RewardData[] = [];
  Object.keys(data).forEach((key) => {
    // @ts-ignore
    if (typeof data?.[key] === 'object') {
      // @ts-ignore
      const item = data[key] as RewardStatus;
      const benefitElement = {
        type: key,
        success: item?.success,
        num: isTrue(item?.success) ? item?.num : '-',
        name: BENEFIT_CONFIG_MAP?.[key]?.desc,
        url: BENEFIT_CONFIG_MAP?.[key]?.url,
        logkey: BENEFIT_CONFIG_MAP?.[key]?.logkey,
      };
      result.push(benefitElement);
    }
  });
  return result;
}

function RightsTypesAndNum(props: IUserRightsProps) {
  const { benefitCount } = props || {};

  const couponCss: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'baseline',
  };

  const { benefitList, benefitStyle } = useMemo(() => {
    const resBenefitList = convertToObjectArray(benefitCount);
    const fitFontSizeByDataMaxLen = (benefitListParams: any) => {
      let maxLength = 0;
      benefitListParams?.forEach?.((item: RewardData) => {
        const numString = item.num.toString();
        if (numString.length > maxLength) {
          maxLength = numString.length;
        }
      });
      if (maxLength === 6) {
        return {
          numFontSize: '22px',
          symbolFontSize: '14px',
          couponSymbolFontSize: '12px',
        };
      } else if (maxLength > 6) {
        return {
          numFontSize: '20px',
          symbolFontSize: '12px',
          couponSymbolFontSize: '10px',
        };
      } else {
        return {
          numFontSize: '24px',
          symbolFontSize: '16px',
          couponSymbolFontSize: '14px',
        };
      }
    };
    return {
      benefitList: resBenefitList,
      benefitStyle: fitFontSizeByDataMaxLen(resBenefitList),
    };
  }, [benefitCount]);

  const showLoading = useMemo(() => {
    return (
      <>
        {[...new Array(3)].map((_, index) => {
          return (
            <div className="right-item-skeleton" key={index}>
              <div className="right-item-detail-skeleton skeletonLoading" />
              <span className="right-item-desc-skeleton skeletonLoading" />
            </div>
          );
        })}
      </>
    );
  }, []);

  useEffect(() => {
    if (!isTrue(benefitCount?.isFetching)) {
      goldLogUserExp('rights', 'dredpacket');
      goldLogUserExp('rights', 'dcoupon');
      goldLogUserExp('rights', 'dtaojinbi');
    }
  }, [benefitCount?.isFetching]);

  return (
    <div className="rights-types-and-num-container">
      <div className="rights-types-and-num" data-spm="rights">
        {isTrue(benefitCount?.isFetching) && showLoading}
        {!isTrue(benefitCount?.isFetching) &&
          (benefitList || [])?.map((item) => {
            return (
              <a
                key={item?.logkey}
                href={item?.url}
                target="_blank"
                className="right-item"
                data-spm={item?.logkey}
                onClick={(e) => {
                  // if (item?.type === 'redEnvelop' || item?.type === 'coupon') {
                  //   e.stopPropagation();
                  //   e.preventDefault();
                  //   showBenefitDrawer({ type: item?.type === 'coupon' ? 'coupon' : 'redEnvelop' });
                  // }

                  goldLogUserClk('rights', item?.logkey);
                }}
              >
                <div className="right-item-detail" style={item?.type === 'coupon' ? couponCss : {}}>
                  <span
                    className={item?.type === 'coupon' ? 'right-item-coupon-label' : 'right-item-label'}
                    style={{
                      fontSize:
                        item?.type === 'coupon' ? benefitStyle?.couponSymbolFontSize : benefitStyle?.symbolFontSize,
                    }}
                  >
                    {item?.type === 'coupon' ? '张' : '¥'}
                  </span>
                  <span className="right-item-amount" style={{ fontSize: benefitStyle?.numFontSize }}>
                    {item?.num}
                  </span>
                </div>
                <span className="right-item-desc">{item?.name}</span>
              </a>
            );
          })}
      </div>
    </div>
  );
}

export default RightsTypesAndNum;
