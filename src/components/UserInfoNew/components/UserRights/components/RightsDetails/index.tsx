import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import type { CSSProperties } from 'react';
import { memo, useMemo, useRef } from 'react';
import type { Settings as SliderSettings } from 'react-slick';
// import { showBenefitDrawer } from '../../../../..//BenefitDrawer';
import Slider from '../../../../../Slick/slider.js';
import type {
  CouponData,
  HongBaoData,
  IUserRightsProps,
  LinkData,
} from '../../../../types';
import { goldLogUserClk, goldLogUserExp } from '../../../../utils/index';

import 'slick-carousel/slick/slick.css';
import './index.less';

dayjs.extend(duration);

const DefaultSliderSettings: SliderSettings = {
  dots: false,
  infinite: true,
  autoplay: true,
  autoplaySpeed: 5000,
  arrows: false,
};

const BENEFIT_ICON =
  'https://img.alicdn.com/imgextra/i2/O1CN010ZR9Yk1yV4MWa00iD_!!6000000006583-2-tps-104-104.png';
const COUPON_ICON =
  'https://img.alicdn.com/imgextra/i2/O1CN0133iCFD1kem7vcgB1x_!!6000000004709-2-tps-96-96.png';

const couponStyleMap: {
  [key: number]: {
    symbol: CSSProperties;
    value: CSSProperties;
  };
} = {
  1: {
    symbol: {
      fontSize: '12px',
      lineHeight: '12px',
    },
    value: {
      fontSize: '16px',
      lineHeight: '16px',
    },
  },
  2: {
    symbol: {
      fontSize: '12px',
      lineHeight: '12px',
    },
    value: {
      fontSize: '14px',
      lineHeight: '14px',
    },
  },
  3: {
    symbol: {
      fontSize: '10px',
      lineHeight: '10px',
    },
    value: {
      fontSize: '12px',
      lineHeight: '12px',
    },
  },
};

const getCountdownTime = ({
  currentTimestamp,
  invalidTimestamp,
}: {
  currentTimestamp: number;
  invalidTimestamp: number;
}) => {
  if (invalidTimestamp && currentTimestamp) {
    const _timeLeft = dayjs.duration(
      dayjs(invalidTimestamp).diff(dayjs(currentTimestamp)),
    );
    const _days = Math.floor(_timeLeft.asDays());
    return `${_days > 0 ? `${_days}天` : ''}${_timeLeft.hours() > 0 ? `${_timeLeft.hours()}小时` : `${_timeLeft.minutes()}分钟`}`;
  }
  return '';
};

function renderBenefitUI(
  _slider: HongBaoData | CouponData,
  index: number,
  showAvatarConfig: boolean,
) {
  const isRedPacket = _slider?.type === 'fund';
  return (
    <>
      <div className="rights-details-item-left-container">
        <div className="rights-details-item-pic-container">
          <img
            src={isRedPacket ? BENEFIT_ICON : COUPON_ICON}
            className="rights-details-item-pic"
          />
        </div>
        <div className="rights-details-item-center">
          <div className="rights-details-item-title">
            {`${_slider?.subAssetType === 'zk' && _slider?.displayDiscountRate ? `${_slider?.displayDiscountRate}折` : `${_slider?.displayTotalFee || '**'}元`}`}
            <span className="rights-details-item-title-text">
              {isRedPacket ? '红包' : '优惠券'}
            </span>
          </div>
          <div className="rights-details-item-desc">
            {`仅剩 ${getCountdownTime({
              currentTimestamp: _slider?.currentTimestamp,
              invalidTimestamp: _slider?.invalidTimestamp,
            })}`}
          </div>
        </div>
      </div>
      <div className="rights-details-item-right">
        <div
          className={`rights-details-item-btn ${showAvatarConfig ? 'rights-details-item-btn-vip' : ''}`}
        >
          去使用
        </div>
        {/* <span className="tb-ifont rights-details-item-right-arrow">&#xe642;</span> */}
      </div>
    </>
  );
}

// function renderLinkUi(_link: LinkData, index: number, showAvatarConfig: boolean) {
//   if (_link?.targetUrl && _link?.bottomText) {
//     return (
//       <>
//         <div className="rights-details-item-pic-container">
//           <img src={BENEFIT_ICON} className="rights-details-item-pic" />
//         </div>
//         <div className="rights-details-item-center">
//           <div className="rights-details-item-title">{_link.bottomText}</div>
//         </div>
//         <div className="rights-details-item-right">
//           <span className="tb-ifont rights-details-item-right-arrow">
//             &#xe642;
//           </span>
//         </div>
//       </>
//     );
//   }
//   return null;
// }

function renderNewLinkUi(_link: LinkData, index: number, showAvatarConfig: boolean) {
  if (_link?.targetUrl && _link?.bottomText) {
    return (
      <>
        <div className="rights-details-item-pic-container">
          <img src={BENEFIT_ICON} className="rights-details-item-pic" />
        </div>
        <div className="rights-details-item-center">
          <div className="rights-details-item-title">{_link.bottomText}</div>
          <div className="rights-details-item-title">{_link.subText}</div>
        </div>
        {
          _link.btnText ? <div className="rights-details-item-right">
            <div
              className={`rights-details-item-btn ${showAvatarConfig ? 'rights-details-item-btn-vip' : ''}`}
            >
              {_link.btnText}
            </div>
          </div> : null
        }
      </>
    );
  }
  return null;
}


function RightsDetails(props: IUserRightsProps) {
  const { benefitSliderList, showAvatarConfig } = props || {};
  const hasReported = useRef<Record<number, boolean>>({});

  const benefitSliderListWithStyle = useMemo(() => {
    return benefitSliderList.map((slider) => {
      if (slider?.type === 'coupon') {
        const _slider = slider as CouponData;
        const displayTotalFeeLength = _slider?.displayTotalFee?.length;
        return {
          ..._slider,
          style: couponStyleMap?.[displayTotalFeeLength] || couponStyleMap[3],
        };
      } else {
        return {
          ...slider,
        };
      }
    });
  }, [benefitSliderList]);

  return (
    <div className="rights-details" data-spm="use">
      <Slider
        className="rights-slick-list"
        {...DefaultSliderSettings}
        onInit={() => {
          if (benefitSliderList?.[0] && !hasReported.current[0]) {
            goldLogUserExp('use', `d${1}`);
            hasReported.current[0] = true;
          }
        }}
        afterChange={(current: number) => {
          if (benefitSliderList?.[current] && !hasReported.current[current]) {
            goldLogUserExp('use', `d${current + 1}`);
            hasReported.current[current] = true;
          }
        }}
      >
        {(benefitSliderListWithStyle || [])?.map((slider, index) => {
          const isBenefit =
            slider?.type === 'fund' || slider?.type === 'coupon';
          return (
            <a
              key={`use_${index}`}
              className={'rights-details-item'}
              href={slider?.targetUrl}
              target="_blank"
              data-spm={`d${index + 1}`}
              onClick={(e) => {
                // if (slider?.type === 'coupon' && (slider as CouponData)?.subAssetType !== 'zk') {
                //   e.stopPropagation();
                //   e.preventDefault();
                //   showBenefitDrawer({
                //     type: 'coupon',
                //     couponId: String((slider as CouponData).extraData?.templateCode),
                //     couponGroupId: String((slider as CouponData).extraData?.couponTag),
                //   });
                // }
                goldLogUserClk('use', `d${index + 1}`);
              }}
            >
              {isBenefit
                ? renderBenefitUI(
                    slider as HongBaoData | CouponData,
                    index,
                    showAvatarConfig,
                  )
                : renderNewLinkUi(slider as LinkData, index, showAvatarConfig)}
            </a>
          );
        })}
      </Slider>
    </div>
  );
}

export default memo(RightsDetails);
