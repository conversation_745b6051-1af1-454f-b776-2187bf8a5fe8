import { useMemo } from 'react';
import './index.less';

import { AppearWeb } from '@ali/appear';
import { getValidString } from '../../../../utils/format';
import { reportCustom } from '../../../../utils/jstracker';
import type { IBaseCardProps } from '../../index';
import { getValidLengthCardData } from '../../lib/card';
import { goldLogBusinessEntryClk, goldLogBusinessEntryExp } from '../../lib/tracker';
import Loading from '../../Loading';
import type { BBSCardDetail } from '../../typings';
import CardTitle from '../CardTitle';

const DefaultSpmC = 'yingxiao';
const DefaultScm = '1007.home_pc.taojianghu.item';

const BBS_HOME_ADDRESS = 'https://bbs.taobao.com/home.html';
const sliceCount = 5;

const BbsCard = (props: IBaseCardProps) => {
  const { style, posId, aldData, cachedData, zebraData, backupData } = props;

  const validCard = getValidLengthCardData({
    card: aldData,
    cachedCard: cachedData,
    zebraCard: zebraData,
    backupCard: backupData,
    length: sliceCount,
  });
  const validItems = (validCard?.cardData || []) as BBSCardDetail[];

  if (!aldData?.cardData?.length) {
    reportCustom({
      code: 'module-data',
      message: '[bbs][ald-data][empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
  }

  const slicedData = useMemo(() => {
    return validItems.slice(0, sliceCount);
  }, [validItems]);

  if (!slicedData?.length) {
    reportCustom({
      code: 'empty-transformer-area',
      message: '[bbs][data][sliceddata-empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
    return <Loading key={props.posId} style={props.style} />;
  }

  return (
    <AppearWeb
      onFirstAppear={() => {
        goldLogBusinessEntryExp(posId, 'bbs', { scm: DefaultScm });
      }}
    >
      <div
        className="business-entry-bbs-card"
        data-spm={DefaultSpmC}
        style={{
          ...(style || {}),
          backgroundColor: validCard?.cardColor || style?.backgroundColor,
        }}
      >
        <CardTitle
          cardPrefixIcon={validCard?.cardPrefixIcon || ''}
          cardTitleColor={validCard?.cardTitleColor || ''}
          cardTitle={validCard?.cardTitle || '淘江湖'}
          cardJumpUrl={validCard?.cardJumpUrl || BBS_HOME_ADDRESS}
          cardIcon={validCard?.cardIcon}
          posId={posId}
          contentType={'bbs'}
          scm={DefaultScm}
        />
        {slicedData.length
          ? slicedData.map((e, index: number) => (
            <AppearWeb
              key={index}
              onFirstAppear={() => {
                goldLogBusinessEntryExp(`${posId}_${index + 1}`, 'bbs', {
                  scm: DefaultScm,
                });
              }}
            >
              <a
                key={e.title}
                className="business-entry-bbs-card-content"
                href={e.jumpUrl || ''}
                target="_blank"
                data-spm={`d${props.posId}_${index + 1}`}
                onClick={(e) => {
                  e.stopPropagation();
                  goldLogBusinessEntryClk(`${posId}_${index + 1}`, 'bbs', {
                    scm: DefaultScm,
                  });
                }}
              >
                {e.icon ? <img className="business-entry-bbs-card-content-icon" src={e.icon} /> : null}
                <div
                  className="business-entry-bbs-card-content-desc"
                  style={e.icon ? { width: 'calc(100% - 20px)' } : {}}
                  title={getValidString(e.title) || '淘江湖论坛热帖'}
                >
                  {getValidString(e.title) || '淘江湖论坛热帖'}
                </div>
              </a>
            </AppearWeb>
          ))
          : null}
      </div>
    </AppearWeb>
  );
};

export default BbsCard;
