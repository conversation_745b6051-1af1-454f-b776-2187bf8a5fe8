import { useMemo } from 'react';
import './index.less';

import { AppearWeb } from '@ali/appear';
import { isWapa } from '@/utils/env';
import { getValidString } from '@/utils/format';
import { optimizeImage } from '@/utils/image';
import { reportCustom } from '@/utils/jstracker';
import { addQuery } from '@/utils/url';
import type { IBaseCardProps } from '../../index';
import { getValidLengthCardData } from '../../lib/card';
import { goldLogBusinessEntryClk, goldLogBusinessEntryExp } from '../../lib/tracker';
import Loading from '../../Loading';
import type { LiveCardDetail } from '../../typings';
import CardTitle from '../CardTitle';

const DefaultSpmC = 'yingxiao';
const CURRENT_LIVE_LENGTH = 2;

const LIVE_HOME_ADDRESS = 'https://tbzb.taobao.com';
const LIVE_PRE_HOME_ADDRESS = 'https://pre-wormhole.tmall.com/wow/z/tbhome/live/zpxh5zyi4j5ib25bd2pw';

function LiveCard(props: IBaseCardProps) {
  const { style, posId, aldData, cachedData, zebraData, backupData } = props;

  const validCard = getValidLengthCardData({
    card: aldData,
    cachedCard: cachedData,
    zebraCard: zebraData,
    backupCard: backupData,
    length: CURRENT_LIVE_LENGTH,
  });
  const validItems = (validCard?.cardData || []) as LiveCardDetail[];

  if (!aldData?.cardData?.length) {
    reportCustom({
      code: 'module-data',
      message: '[live][ald-data][empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
  }

  const slicedData = useMemo(() => {
    return validItems.slice(0, CURRENT_LIVE_LENGTH);
  }, [validItems]);

  if (!slicedData?.length) {
    reportCustom({
      code: 'empty-transformer-area',
      message: '[yingxiao][live][sliceddata-empty]',
      sampling: 1,
      c1: JSON.stringify(props),
    });
    return <Loading style={props.style} />;
  }

  return (
    <AppearWeb
      onFirstAppear={() => {
        goldLogBusinessEntryExp(posId, 'live');
      }}
    >
      <div
        className="business-entry-live-card"
        data-spm={DefaultSpmC}
        style={{
          ...(style || {}),
          backgroundColor: validCard?.cardColor || style?.backgroundColor,
        }}
      >
        <CardTitle
          cardPrefixIcon={validCard?.cardPrefixIcon || ''}
          cardTitleColor={validCard?.cardTitleColor || ''}
          cardTitle={validCard?.cardTitle || '淘宝直播'}
          cardJumpUrl={validCard?.cardJumpUrl || (isWapa ? LIVE_PRE_HOME_ADDRESS : LIVE_HOME_ADDRESS)}
          cardIcon={validCard?.cardIcon}
          posId={posId}
          contentType={'live'}
        />
        <div className="business-entry-live-card-content-container">
          {slicedData.map((item, index) => {
            return (
              <AppearWeb
                key={`${item?.liveId}_${index}`}
                onFirstAppear={() => {
                  goldLogBusinessEntryExp(`${posId}_${index + 1}`, 'live', {
                    liveId: item?.liveId || '',
                  });
                }}
              >
                <a
                  className="business-entry-live-card-content"
                  style={{ marginLeft: index === 0 ? 0 : '8px' }}
                  href={addQuery(
                    {
                      liveId: item?.liveId || '',
                    },
                    item?.liveHallUrl || (isWapa ? LIVE_PRE_HOME_ADDRESS : LIVE_HOME_ADDRESS),
                  )}
                  target="_blank"
                  data-spm={`d${posId}_${index + 1}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    goldLogBusinessEntryClk(`${posId}_${index + 1}`, 'live', {
                      liveId: item?.liveId || '',
                    });
                  }}
                >
                  <div
                    className="business-entry-live-card-inner"
                    style={{ backgroundImage: `url(${optimizeImage(item?.imgUrl)})` }}
                  >
                    <div className="business-entry-live-card-inner-mask" />
                    <div
                      className="business-entry-live-card-inner-title"
                      title={getValidString(item?.title) || '好物推荐'}
                    >
                      {getValidString(item?.title) || '好物推荐'}
                    </div>
                    <img
                      className="business-entry-live-card-inner-atmosphere"
                      src={
                        item?.liveExtUrl ||
                        'https://gw.alicdn.com/imgextra/i3/O1CN01Hac6GN1NhJ3mEoPB7_!!6000000001601-1-tps-56-56.gif'
                      }
                      alt=""
                    />
                  </div>
                </a>
              </AppearWeb>
            );
          })}
        </div>
      </div>
    </AppearWeb>
  );
}

export default LiveCard;
