import { reportCustom } from './jstracker';

/**
 * 存储数据到本地存储
 * @param key 存储的键
 * @param value 存储的值
 */
export const setLocalStorageItem = (key: string, value: any) => {
  try {
    window.localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    reportCustom({
      code: 'localStorage-failed',
      message: '[utils][storage][localStorage][set error]',
      sampling: 1,
      c1: JSON.stringify(error?.message || 'set localStorage onerror triggered'),
    });
    return false;
  }
};

/**
 * 从本地存储获取数据
 * @param key 存储的键
 * @returns 存储的值或null（如果键不存在或解析失败）
 */
export const getLocalStorageItem = (key: string): any | null => {
  try {
    const theValue = window.localStorage.getItem(key);
    return theValue ? JSON.parse(theValue) : null;
  } catch (error) {
    reportCustom({
      code: 'localStorage-failed',
      message: '[utils][storage][localStorage][get error]',
      sampling: 1,
      c1: JSON.stringify(error?.message || 'get localStorage onerror triggered'),
    });
    return null;
  }
};

/**
 * 组件缓存管理器 - 最小改动版本
 * 复用现有的 getLocalStorageItem 和 setLocalStorageItem
 * 支持组件内部定义key和多个id形式
 */
export function createComponentCache<T = any>(
  keyGenerator: (...ids: (string | number)[]) => string,
  prefix: string = 'PC_INDEX_CLIENT_dataCache_taocoin'
) {
  const generateFullKey = (...ids: (string | number)[]) => {
    const componentKey = keyGenerator(...ids);
    return `${prefix}_${componentKey}`;
  };

  return {
    /**
     * 设置缓存数据
     * @param data 要缓存的数据
     * @param ids 用于生成key的id参数
     * @returns 是否设置成功
     */
    setCache: (data: T, ...ids: (string | number)[]): boolean => {
      const fullKey = generateFullKey(...ids);
      return setLocalStorageItem(fullKey, data);
    },

    /**
     * 获取缓存数据
     * @param ids 用于生成key的id参数
     * @returns 缓存的数据或null
     */
    getCache: (...ids: (string | number)[]): T | null => {
      const fullKey = generateFullKey(...ids);
      return getLocalStorageItem(fullKey);
    },

    /**
     * 删除缓存数据
     * @param ids 用于生成key的id参数
     * @returns 是否删除成功
     */
    removeCache: (...ids: (string | number)[]): boolean => {
      try {
        const fullKey = generateFullKey(...ids);
        window.localStorage.removeItem(fullKey);
        return true;
      } catch (error) {
        reportCustom({
          code: 'localStorage-failed',
          message: '[utils][storage][localStorage][remove error]',
          sampling: 1,
          c1: JSON.stringify(error?.message || 'remove localStorage onerror triggered'),
        });
        return false;
      }
    },

    /**
     * 清除所有相关缓存
     * @returns 清除的数量
     */
    clearAllCache: (): number => {
      try {
        let count = 0;
        const keys = Object.keys(localStorage);
        const prefixToMatch = `${prefix}_`;

        keys.forEach(key => {
          if (key.startsWith(prefixToMatch)) {
            localStorage.removeItem(key);
            count++;
          }
        });

        return count;
      } catch (error) {
        reportCustom({
          code: 'localStorage-failed',
          message: '[utils][storage][localStorage][clear error]',
          sampling: 1,
          c1: JSON.stringify(error?.message || 'clear localStorage onerror triggered'),
        });
        return 0;
      }
    }
  };
}
